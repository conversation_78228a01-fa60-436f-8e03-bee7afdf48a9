{"name": "PCB-WX", "version": "1.0.0", "description": "", "private": true, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "rtl": "webpack --config webpack-rtl.config.js"}, "dependencies": {"@babel/polyfill": "^7.4.4", "@fortawesome/fontawesome-free": "^5.13.0", "@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/list": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@fullcalendar/vue": "^6.1.17", "@mdi/font": "^3.6.95", "@popperjs/core": "^2.4.0", "apexcharts": "^3.19.0", "axios": "^0.19.2", "axios-mock-adapter": "^1.18.1", "bootstrap": "^4.5.0", "bootstrap-vue": "^2.13.0", "clipboard": "^2.0.6", "core-js": "^3.6.5", "crypto-js": "^4.2.0", "daterangepicker": "^3.1.0", "deepmerge": "^4.2.2", "dom-to-image": "^2.6.0", "echarts": "^5.6.0", "highlight.js": "^9.18.1", "html2canvas": "^1.4.1", "jquery": "^3.7.1", "line-awesome": "^1.3.0", "moment": "^2.30.1", "object-path": "^0.11.4", "perfect-scrollbar": "^1.5.0", "pinia": "^3.0.3", "portal-vue": "^2.1.7", "roboto-fontface": "*", "socicon": "^3.0.5", "sweetalert2": "^9.10.12", "tooltip.js": "^1.3.2", "vue": "^2.6.11", "vue-apexcharts": "^1.5.3", "vue-axios": "^2.1.4", "vue-cropperjs": "^4.1.0", "vue-highlight.js": "^3.1.0", "vue-i18n": "^8.28.2", "vue-inline-svg": "^1.3.0", "vue-router": "^3.1.5", "vue-select": "^2.6.4", "vue-tree-color": "^2.3.2", "vue2-daterange-picker": "^0.6.8", "vue2-perfect-scrollbar": "^1.5.0", "vuelidate": "^0.7.5", "vuetify": "^2.2.26", "vuex": "^3.3.0", "weixin-js-sdk": "^1.6.5", "xlsx": "^0.18.5", "yarn": "^1.22.22"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.3.1", "@vue/cli-plugin-eslint": "^4.3.1", "@vue/cli-service": "^4.3.1", "@vue/eslint-config-prettier": "^4.0.1", "babel-eslint": "^10.0.3", "eslint": "^5.16.0", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^5.0.0", "less": "^3.9.0", "less-loader": "^4.1.0", "sass": "^1.26.5", "sass-loader": "^8.0.2", "vue-cli-plugin-vuetify": "^2.0.5", "vue-template-compiler": "^2.6.11", "vuetify-loader": "^1.3.0", "webpack-cli": "^3.3.11", "webpack-messages": "^2.0.4", "webpack-rtl-plugin": "^2.0.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "rules": [], "parserOptions": {"parser": "babel-es<PERSON>"}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "author": "凡遵德", "license": "ISC"}